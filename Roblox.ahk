; AutoHotkey v2 - Script de Mineração para Roblox
; Pressiona F1 para iniciar/parar o script

#Requires AutoHotkey v2.0
#SingleInstance Force

; Variáveis globais
mining := false
windowTitle := "Roblox"
orePositions := []  ; Array para guardar posições dos minérios
currentOreIndex := 1
miningMode := "pattern"  ; "pattern" ou "ores"
recordingPosition := false

; Função para verificar se a janela do Roblox está ativa
CheckRobloxWindow() {
    if !WinActive(windowTitle) {
        MsgBox("Por favor, certifica-te que a janela do Roblox está ativa!", "Aviso", 0x40)
        return false
    }
    return true
}

; Função para obter a posição atual do cursor (representa a posição do personagem)
GetCurrentPosition() {
    MouseGetPos(&x, &y)
    return {x: x, y: y, timestamp: A_Now}
}

; Função para adicionar uma nova posição de minério
AddOrePosition() {
    if !CheckRobloxWindow()
        return

    pos := GetCurrentPosition()
    orePositions.Push(pos)

    ToolTip("Posição " . orePositions.Length . " gravada!`nX: " . pos.x . " Y: " . pos.y, 10, 10)
    SetTimer(() => ToolTip(), -2000)  ; Remove tooltip após 2 segundos

    ; Guarda as posições num ficheiro
    SaveOrePositions()
}

; Função para guardar posições num ficheiro
SaveOrePositions() {
    try {
        fileContent := "; Posições de Minérios - " . A_Now . "`n"
        for index, pos in orePositions {
            fileContent .= index . "," . pos.x . "," . pos.y . "," . pos.timestamp . "`n"
        }
        FileDelete("ore_positions.txt")
        FileAppend(fileContent, "ore_positions.txt")
    } catch Error as e {
        MsgBox("Erro ao guardar posições: " . e.message, "Erro", 0x10)
    }
}

; Função para carregar posições do ficheiro
LoadOrePositions() {
    try {
        if !FileExist("ore_positions.txt")
            return

        orePositions := []
        content := FileRead("ore_positions.txt")
        lines := StrSplit(content, "`n")

        for line in lines {
            if (line = "" || SubStr(line, 1, 1) = ";")
                continue

            parts := StrSplit(line, ",")
            if (parts.Length >= 4) {
                orePositions.Push({
                    x: Integer(parts[2]),
                    y: Integer(parts[3]),
                    timestamp: parts[4]
                })
            }
        }

        if (orePositions.Length > 0) {
            MsgBox("Carregadas " . orePositions.Length . " posições de minérios!", "Sucesso", 0x40)
        }
    } catch Error as e {
        MsgBox("Erro ao carregar posições: " . e.message, "Erro", 0x10)
    }
}

; Função para mover o cursor (e consequentemente o personagem) para uma posição
MoveToPosition(targetPos) {
    MouseMove(targetPos.x, targetPos.y, 2)  ; Move o cursor rapidamente
    Sleep(500)  ; Pequena pausa para estabilizar
}

; Função principal de mineração
StartMining() {
    if !CheckRobloxWindow()
        return

    mining := true

    if (miningMode = "ores" && orePositions.Length = 0) {
        MsgBox("Nenhuma posição de minério gravada!`nUsa F8 para gravar posições ou F6 para mudar para modo padrão.", "Aviso", 0x40)
        mining := false
        return
    }

    if (miningMode = "ores") {
        ToolTip("Mineração ATIVA (Modo Ores) - " . orePositions.Length . " posições`nPressiona F1 para parar", 10, 10)
        StartOreMining()
    } else {
        ToolTip("Mineração ATIVA (Modo Padrão) - Pressiona F1 para parar", 10, 10)
        StartPatternMining()
    }
}

; Função de mineração por posições de ores
StartOreMining() {
    currentOreIndex := 1

    while (mining) {
        ; Verifica se a janela do Roblox ainda está ativa
        if !WinActive(windowTitle) {
            StopMining()
            MsgBox("Janela do Roblox perdeu o foco. Script pausado.", "Aviso", 0x40)
            break
        }

        ; Vai para a posição atual do ore
        currentOre := orePositions[currentOreIndex]
        MoveToPosition(currentOre)

        ; Atualiza tooltip com informação atual
        ToolTip("Minerando Ore " . currentOreIndex . "/" . orePositions.Length . "`nPosição: " . currentOre.x . "," . currentOre.y . "`nF1 para parar", 10, 10)

        ; Espera 3 segundos para mineração
        Sleep(3000)

        ; Move para o próximo ore
        currentOreIndex++
        if (currentOreIndex > orePositions.Length)
            currentOreIndex := 1

        ; Pequena pausa entre ores
        Sleep(1000)
    }
}

; Função de mineração por padrão (código original)
StartPatternMining() {
    ; Padrão de movimento (podes ajustar conforme necessário)
    movePattern := [
        {key: "w", duration: 1000},    ; Andar para frente 1 segundo
        {key: "d", duration: 500},     ; Andar para direita 0.5 segundos
        {key: "s", duration: 1000},    ; Andar para trás 1 segundo
        {key: "a", duration: 500},     ; Andar para esquerda 0.5 segundos
    ]

    patternIndex := 1

    while (mining) {
        ; Verifica se a janela do Roblox ainda está ativa
        if !WinActive(windowTitle) {
            StopMining()
            MsgBox("Janela do Roblox perdeu o foco. Script pausado.", "Aviso", 0x40)
            break
        }

        ; Executa o movimento atual do padrão
        currentMove := movePattern[patternIndex]

        ; Pressiona e mantém a tecla de movimento
        Send("{" . currentMove.key . " down}")
        Sleep(currentMove.duration)
        Send("{" . currentMove.key . " up}")

        ; Espera 2 segundos para a mineração (tempo que mencionaste)
        Sleep(2000)

        ; Move para o próximo movimento no padrão
        patternIndex++
        if (patternIndex > movePattern.Length)
            patternIndex := 1

        ; Pequena pausa entre movimentos
        Sleep(500)
    }
}

; Função para parar a mineração
StopMining() {
    mining := false
    ToolTip()
    ; Para qualquer movimento que possa estar a acontecer
    Send("{w up}{a up}{s up}{d up}")
}

; Hotkeys
F1:: {
    if (mining) {
        StopMining()
        MsgBox("Mineração PARADA", "Status", 0x40)
    } else {
        StartMining()
    }
}

; Hotkey de emergência para parar tudo
F2:: {
    StopMining()
    MsgBox("PARADA DE EMERGÊNCIA - Script parado!", "Emergência", 0x30)
}

; Sair do script
F3:: {
    StopMining()
    ExitApp()
}

; Alternar entre modo padrão e modo ores
F6:: {
    if (miningMode = "pattern") {
        miningMode := "ores"
        MsgBox("Modo alterado para: ORES`n`nPosições gravadas: " . orePositions.Length . "`nUsa F8 para gravar novas posições", "Modo Ores", 0x40)
    } else {
        miningMode := "pattern"
        MsgBox("Modo alterado para: PADRÃO`n`nO script usará o movimento em padrão original", "Modo Padrão", 0x40)
    }
}

; Mostrar posições gravadas
F7:: {
    if (orePositions.Length = 0) {
        MsgBox("Nenhuma posição de ore gravada ainda.`n`nUsa F8 para gravar posições!", "Lista de Ores", 0x40)
        return
    }

    listText := "=== POSIÇÕES DE ORES GRAVADAS ===`n`n"
    for index, pos in orePositions {
        listText .= "Ore " . index . ": X=" . pos.x . " Y=" . pos.y . "`n"
    }
    listText .= "`nTotal: " . orePositions.Length . " posições"
    listText .= "`n`nF9 para limpar todas as posições"

    MsgBox(listText, "Lista de Ores", 0x40)
}

; Gravar nova posição de ore
F8:: {
    if (recordingPosition) {
        return  ; Evita gravações duplas
    }

    recordingPosition := true
    ToolTip("A gravar posição...", 10, 10)
    Sleep(500)  ; Pequena pausa para o utilizador se posicionar

    AddOrePosition()
    recordingPosition := false
}

; Limpar todas as posições
F9:: {
    result := MsgBox("Tens a certeza que queres limpar todas as posições de ores?`n`nEsta ação não pode ser desfeita!", "Confirmar", 0x34)
    if (result = "Yes") {
        orePositions := []
        try {
            FileDelete("ore_positions.txt")
        }
        MsgBox("Todas as posições foram limpas!", "Sucesso", 0x40)
    }
}

; Mostrar ajuda
F4:: {
    helpText := "
    (
    === CONTROLOS DO SCRIPT ===

    F1: Iniciar/Parar mineração
    F2: Parada de emergência
    F3: Sair do script
    F4: Mostrar esta ajuda
    F6: Alternar modo (Padrão/Ores)
    F7: Ver posições gravadas
    F8: Gravar posição atual
    F9: Limpar todas as posições

    === MODO PADRÃO ===

    1. Abre o Roblox e vai para o jogo
    2. Posiciona o personagem numa área com ores
    3. Pressiona F1 para começar
    4. O script move em padrão automático

    === MODO ORES ===

    1. Posiciona-te ao lado de um ore
    2. Pressiona F8 para gravar a posição
    3. Repete para todos os ores que quiseres
    4. Pressiona F6 para mudar para modo Ores
    5. Pressiona F1 para começar a mineração
    6. O script irá circular entre as posições gravadas

    === DICAS ===

    - As posições são guardadas automaticamente
    - Usa F7 para ver todas as posições gravadas
    - F9 limpa todas as posições (cuidado!)
    - O script para se a janela Roblox perder foco
    )"

    MsgBox(helpText, "Ajuda - Script de Mineração Avançado", 0x40)
}

; Inicialização
LoadOrePositions()  ; Carrega posições guardadas anteriormente
MsgBox("Script de Mineração Avançado carregado!`n`nModo atual: " . StrUpper(miningMode) . "`nPosições carregadas: " . orePositions.Length . "`n`nPressiona F4 para ver todos os controlos.", "Pronto", 0x40)