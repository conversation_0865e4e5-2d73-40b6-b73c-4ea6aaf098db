; AutoHotkey v2 - Script de Mineração para Roblox
; Pressiona F1 para iniciar/parar o script

#Requires AutoHotkey v2.0
#SingleInstance Force

; Variáveis globais
mining := false
windowTitle := "Roblox"

; Função para verificar se a janela do Roblox está ativa
CheckRobloxWindow() {
    if !WinActive(windowTitle) {
        MsgBox("Por favor, certifica-te que a janela do Roblox está ativa!", "Aviso", 0x40)
        return false
    }
    return true
}

; Função principal de mineração
StartMining() {
    if !CheckRobloxWindow()
        return
    
    mining := true
    ToolTip("Mineração ATIVA - Pressiona F1 para parar", 10, 10)
    
    ; Padrão de movimento (podes ajustar conforme necessário)
    movePattern := [
        {key: "w", duration: 1000},    ; Andar para frente 1 segundo
        {key: "d", duration: 500},     ; Andar para direita 0.5 segundos
        {key: "s", duration: 1000},    ; Andar para trás 1 segundo
        {key: "a", duration: 500},     ; Andar para esquerda 0.5 segundos
    ]
    
    patternIndex := 1
    
    while (mining) {
        ; Verifica se a janela do Roblox ainda está ativa
        if !WinActive(windowTitle) {
            StopMining()
            MsgBox("Janela do Roblox perdeu o foco. Script pausado.", "Aviso", 0x40)
            break
        }
        
        ; Executa o movimento atual do padrão
        currentMove := movePattern[patternIndex]
        
        ; Pressiona e mantém a tecla de movimento
        Send("{" . currentMove.key . " down}")
        Sleep(currentMove.duration)
        Send("{" . currentMove.key . " up}")
        
        ; Espera 2 segundos para a mineração (tempo que mencionaste)
        Sleep(2000)
        
        ; Move para o próximo movimento no padrão
        patternIndex++
        if (patternIndex > movePattern.Length)
            patternIndex := 1
        
        ; Pequena pausa entre movimentos
        Sleep(500)
    }
}

; Função para parar a mineração
StopMining() {
    mining := false
    ToolTip()
    ; Para qualquer movimento que possa estar a acontecer
    Send("{w up}{a up}{s up}{d up}")
}

; Hotkeys
F1:: {
    if (mining) {
        StopMining()
        MsgBox("Mineração PARADA", "Status", 0x40)
    } else {
        StartMining()
    }
}

; Hotkey de emergência para parar tudo
F2:: {
    StopMining()
    MsgBox("PARADA DE EMERGÊNCIA - Script parado!", "Emergência", 0x30)
}

; Sair do script
F3:: {
    StopMining()
    ExitApp()
}

; Mostrar ajuda
F4:: {
    helpText := "
    (
    === CONTROLOS DO SCRIPT ===
    
    F1: Iniciar/Parar mineração
    F2: Parada de emergência
    F3: Sair do script
    F4: Mostrar esta ajuda
    
    === INSTRUÇÕES ===
    
    1. Abre o Roblox e vai para o jogo
    2. Posiciona o personagem numa área com ores
    3. Pressiona F1 para começar
    4. O script irá mover o personagem automaticamente
    5. Espera 2 segundos em cada posição para mineração
    6. Pressiona F1 novamente para parar
    
    === PERSONALIZAÇÃO ===
    
    Podes editar o padrão de movimento no código
    alterando os valores de 'movePattern'
    )"
    
    MsgBox(helpText, "Ajuda - Script de Mineração", 0x40)
}

; Inicialização
MsgBox("Script de Mineração carregado!`n`nPressiona F4 para ver os controlos.", "Pronto", 0x40)